"""
Supabase client module for accessing storage and database.
"""
import os
import logging
from typing import Optional, Dict, Any
from supabase import create_client, Client
import tempfile
import requests

logger = logging.getLogger(__name__)

class SupabaseStorageClient:
    """Manages Supabase storage operations for video access."""
    
    def __init__(self):
        """Initialize Supabase client."""
        self.client: Optional[Client] = None
        self.bucket_name = "soyrunningvideos"  # Default bucket name
        self._initialize_client()
    
    def _initialize_client(self) -> bool:
        """Initialize Supabase client with environment variables."""
        try:
            supabase_url = os.getenv('SUPABASE_URL')
            supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
            
            if not supabase_url or not supabase_service_key:
                logger.error("Missing Supabase environment variables (SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY)")
                return False
            
            logger.debug(f"[DEBUG] Using Supabase URL: {supabase_url}")
            logger.debug(f"[DEBUG] Has service key: {bool(supabase_service_key)}")
            logger.debug(f"[DEBUG] Creating service client with URL: {supabase_url}")
            logger.debug(f"[DEBUG] Service key prefix: {supabase_service_key[:10]}***")
            
            self.client = create_client(supabase_url, supabase_service_key)
            logger.info("Successfully initialized Supabase client")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            self.client = None
            return False
    
    def create_signed_url(self, file_path: str, expires_in: int = 3600) -> Optional[str]:
        """
        Create a signed URL for a file in Supabase storage.
        
        Args:
            file_path: Path to the file in storage (e.g., "folder/file.mp4")
            expires_in: URL expiration time in seconds (default: 1 hour)
            
        Returns:
            Signed URL string or None if failed
        """
        if not self.client:
            logger.error("Supabase client not initialized")
            return None
        
        try:
            logger.debug(f"[DEBUG] Creating signed URL for {self.bucket_name}:{file_path}")
            
            response = self.client.storage.from_(self.bucket_name).create_signed_url(
                file_path, expires_in
            )
            
            if response and 'signedURL' in response:
                signed_url = response['signedURL']
                logger.debug(f"[DEBUG] Successfully created signed URL (expires in {expires_in}s)")
                return signed_url
            else:
                logger.error(f"Failed to create signed URL: {response}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating signed URL for {file_path}: {e}")
            return None
    
    def download_file_to_temp(self, file_path: str) -> Optional[str]:
        """
        Download a file from Supabase storage to a temporary local file.
        
        Args:
            file_path: Path to the file in storage
            
        Returns:
            Path to temporary file or None if failed
        """
        if not self.client:
            logger.error("Supabase client not initialized")
            return None
        
        try:
            # Create signed URL first
            signed_url = self.create_signed_url(file_path, expires_in=1800)  # 30 minutes
            if not signed_url:
                logger.error(f"Failed to create signed URL for {file_path}")
                return None
            
            # Download the file using the signed URL
            logger.debug(f"Downloading file from signed URL: {file_path}")
            response = requests.get(signed_url, stream=True, timeout=300)
            response.raise_for_status()
            
            # Create temporary file
            file_extension = os.path.splitext(file_path)[1] or '.mp4'
            temp_file = tempfile.NamedTemporaryFile(
                delete=False, 
                suffix=file_extension,
                prefix='supabase_video_'
            )
            
            # Write content to temporary file
            with temp_file as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Successfully downloaded {file_path} to {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            return None
    
    def extract_storage_path_from_url(self, url: str) -> Optional[str]:
        """
        Extract the storage path from a Supabase signed URL.
        
        Args:
            url: Supabase signed URL
            
        Returns:
            Storage path or None if not a valid Supabase URL
        """
        try:
            # Check if this is a Supabase storage URL
            if 'supabase.co/storage/v1/object/sign/' in url:
                # Extract the path after the bucket name
                parts = url.split('/storage/v1/object/sign/')
                if len(parts) > 1:
                    # Remove query parameters
                    path_with_params = parts[1]
                    path = path_with_params.split('?')[0]
                    
                    # Remove bucket name from the beginning if present
                    if path.startswith(f'{self.bucket_name}/'):
                        path = path[len(f'{self.bucket_name}/'):]
                    
                    return path
            
            # If not a signed URL, check if it's a direct storage path
            elif url.startswith(f'{self.bucket_name}/'):
                return url[len(f'{self.bucket_name}/'):]
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting storage path from URL: {e}")
            return None

# Global Supabase storage client instance
storage_client = None

def get_supabase_storage_client() -> Optional[SupabaseStorageClient]:
    """Get or create Supabase storage client instance."""
    global storage_client
    
    if storage_client is None:
        storage_client = SupabaseStorageClient()
    
    return storage_client if storage_client.client else None

def cleanup_supabase_client():
    """Cleanup Supabase client on shutdown."""
    global storage_client
    storage_client = None
